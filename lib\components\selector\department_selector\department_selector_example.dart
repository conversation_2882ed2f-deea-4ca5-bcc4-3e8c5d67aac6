import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'department_tree.dart';
import 'department_selector_provider.dart';

/// 部门选择器使用示例
///
/// 展示如何使用新的搜索功能和 Provider 状态管理
class DepartmentSelectorExample extends StatefulWidget {
  const DepartmentSelectorExample({super.key});

  @override
  State<DepartmentSelectorExample> createState() => _DepartmentSelectorExampleState();
}

class _DepartmentSelectorExampleState extends State<DepartmentSelectorExample> {
  final TextEditingController _searchController = TextEditingController();
  final GlobalKey<DepartmentTreeState> _treeKey = GlobalKey<DepartmentTreeState>();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('部门选择器示例'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: ChangeNotifierProvider(
        create: (_) => DepartmentSelectorProvider(),
        child: Column(
          children: [
            // 搜索栏
            _buildSearchBar(),

            // 操作按钮栏
            _buildActionBar(),

            // 部门树
            Expanded(child: _buildDepartmentTree()),

            // 选中结果显示
            _buildSelectedResults(),
          ],
        ),
      ),
    );
  }

  /// 构建搜索栏
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: '搜索部门名称...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon:
              _searchController.text.isNotEmpty
                  ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      _searchController.clear();
                      _performSearch('');
                    },
                  )
                  : null,
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.0)),
        ),
        onChanged: _performSearch,
      ),
    );
  }

  /// 构建操作按钮栏
  Widget _buildActionBar() {
    return Consumer<DepartmentSelectorProvider>(
      builder: (context, provider, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            children: [
              ElevatedButton.icon(
                onPressed: provider.isLoading ? null : () => provider.refresh(),
                icon:
                    provider.isLoading
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.refresh),
                label: Text(provider.isLoading ? '加载中...' : '刷新'),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                onPressed:
                    provider.selectedDepartmentIds.isEmpty
                        ? null
                        : () => provider.clearAllSelections(),
                icon: const Icon(Icons.clear_all),
                label: const Text('清除选择'),
              ),
              const Spacer(),
              Text(
                '已选择: ${provider.selectedDepartmentIds.length} 个部门',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建部门树
  Widget _buildDepartmentTree() {
    return Container(
      margin: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: DepartmentTree(
        key: _treeKey,
        showCheckbox: true,
        onNodeTap: _handleNodeTap,
        onNodeSelected: _handleNodeSelected,
      ),
    );
  }

  /// 构建选中结果显示
  Widget _buildSelectedResults() {
    return Consumer<DepartmentSelectorProvider>(
      builder: (context, provider, child) {
        final selectedDepartments = provider.getSelectedDepartments();

        if (selectedDepartments.isEmpty) {
          return Container(
            padding: const EdgeInsets.all(16.0),
            child: const Text('暂无选中的部门', style: TextStyle(color: Colors.grey)),
          );
        }

        return Container(
          height: 120,
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '已选中的部门 (${selectedDepartments.length}):',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              const SizedBox(height: 8),
              Expanded(
                child: ListView.builder(
                  itemCount: selectedDepartments.length,
                  itemBuilder: (context, index) {
                    final dept = selectedDepartments[index];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 4),
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primaryContainer,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            dept.departmentName,
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onPrimaryContainer,
                            ),
                          ),
                          const SizedBox(width: 8),
                          GestureDetector(
                            onTap: () => provider.toggleDepartmentSelection(dept.id!),
                            child: Icon(
                              Icons.close,
                              size: 16,
                              color: Theme.of(context).colorScheme.onPrimaryContainer,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 执行搜索
  void _performSearch(String query) {
    _treeKey.currentState?.search(query);
  }

  /// 处理节点点击
  void _handleNodeTap(DepartmentModel department) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('点击了部门: ${department.departmentName}'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  /// 处理节点选择
  void _handleNodeSelected(DepartmentModel department, bool isSelected) {
    final action = isSelected ? '选中' : '取消选中';
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$action了部门: ${department.departmentName}'),
        duration: const Duration(seconds: 1),
      ),
    );
  }
}

/// 简单的使用示例（使用独立的 Provider）
class SimpleDepartmentSelectorExample extends StatefulWidget {
  const SimpleDepartmentSelectorExample({super.key});

  @override
  State<SimpleDepartmentSelectorExample> createState() => _SimpleDepartmentSelectorExampleState();
}

class _SimpleDepartmentSelectorExampleState extends State<SimpleDepartmentSelectorExample> {
  final TextEditingController _searchController = TextEditingController();
  final GlobalKey<DepartmentTreeState> _treeKey = GlobalKey<DepartmentTreeState>();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('简单部门选择器示例'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: ChangeNotifierProvider(
        create: (_) => DepartmentSelectorProvider(),
        child: Column(
          children: [
            // 搜索栏
            Container(
              padding: const EdgeInsets.all(16.0),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: '搜索部门名称...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon:
                      _searchController.text.isNotEmpty
                          ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              _treeKey.currentState?.search('');
                            },
                          )
                          : null,
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.0)),
                ),
                onChanged: (query) => _treeKey.currentState?.search(query),
              ),
            ),

            // 部门树（不使用 Provider）
            Expanded(
              child: Container(
                margin: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: DepartmentTree(
                  key: _treeKey,
                  showCheckbox: true,
                  onNodeTap: (department) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('点击了部门: ${department.departmentName}'),
                        duration: const Duration(seconds: 1),
                      ),
                    );
                  },
                  onNodeSelected: (department, isSelected) {
                    final action = isSelected ? '选中' : '取消选中';
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('$action了部门: ${department.departmentName}'),
                        duration: const Duration(seconds: 1),
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
