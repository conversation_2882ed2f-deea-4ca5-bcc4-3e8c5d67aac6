import 'package:flutter/material.dart';
import 'package:octasync_client/api/department.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/models/pages_model/pages_model.dart';

/// 部门选择器状态管理类
/// 
/// 管理部门数据、搜索状态、选中状态等
class DepartmentSelectorProvider with ChangeNotifier {
  // 私有变量区域
  final Map<String, dynamic> _reqParams = {'PageIndex': 1, 'PageSize': 9999};
  PagesModel<DepartmentModel> _pages = PagesModel();
  List<DepartmentModel> _allDepartments = [];
  List<DepartmentModel> _filteredDepartments = [];
  String? _searchQuery;
  bool _isLoading = false;
  String? _errorMessage;
  
  // 选中状态管理
  final Set<String> _selectedDepartmentIds = <String>{};
  String? _highlightedDepartmentId; // 高亮显示的部门ID（单选）
  
  // 展开状态管理
  final Set<String> _expandedDepartmentIds = <String>{};

  // 公开属性区域
  
  /// 获取所有部门数据
  List<DepartmentModel> get allDepartments => _allDepartments;
  
  /// 获取过滤后的部门数据
  List<DepartmentModel> get filteredDepartments => _filteredDepartments;
  
  /// 获取当前搜索查询
  String? get searchQuery => _searchQuery;
  
  /// 获取加载状态
  bool get isLoading => _isLoading;
  
  /// 获取错误信息
  String? get errorMessage => _errorMessage;
  
  /// 获取分页信息
  PagesModel<DepartmentModel> get pages => _pages;
  
  /// 获取所有选中的部门ID
  Set<String> get selectedDepartmentIds => Set.from(_selectedDepartmentIds);
  
  /// 获取高亮显示的部门ID
  String? get highlightedDepartmentId => _highlightedDepartmentId;
  
  /// 获取所有展开的部门ID
  Set<String> get expandedDepartmentIds => Set.from(_expandedDepartmentIds);

  // 构造函数
  DepartmentSelectorProvider() {
    loadDepartments();
  }

  // 公开方法区域
  
  /// 加载部门数据
  Future<void> loadDepartments() async {
    _setLoading(true);
    _setError(null);
    
    try {
      final result = await DepartmentApi.getList(_reqParams);
      _pages = PagesModel.fromJson(
        result,
        (json) => DepartmentModel.fromJson(json as Map<String, dynamic>),
      );
      _allDepartments = _pages.items;
      
      // 默认展开第一个根节点
      _initializeDefaultExpansion();
      
      // 应用当前搜索过滤
      _applySearchFilter();
      
    } catch (error) {
      _setError('加载部门数据失败: $error');
    } finally {
      _setLoading(false);
    }
  }
  
  /// 刷新部门数据
  Future<void> refresh() async {
    await loadDepartments();
  }
  
  /// 搜索部门
  /// 
  /// [searchQuery] 搜索关键词，null或空字符串时显示所有部门
  void search(String? searchQuery) {
    _searchQuery = searchQuery?.trim();
    _applySearchFilter();
    notifyListeners();
  }
  
  /// 设置部门高亮状态（单选模式）
  void setHighlightedDepartment(String? departmentId) {
    if (_highlightedDepartmentId != departmentId) {
      _highlightedDepartmentId = departmentId;
      notifyListeners();
    }
  }
  
  /// 切换部门选中状态（多选模式）
  void toggleDepartmentSelection(String departmentId, {bool? forceValue}) {
    final bool newValue = forceValue ?? !_selectedDepartmentIds.contains(departmentId);
    
    if (newValue) {
      _selectedDepartmentIds.add(departmentId);
    } else {
      _selectedDepartmentIds.remove(departmentId);
    }
    
    notifyListeners();
  }
  
  /// 清除所有选中状态
  void clearAllSelections() {
    if (_selectedDepartmentIds.isNotEmpty) {
      _selectedDepartmentIds.clear();
      notifyListeners();
    }
  }
  
  /// 切换部门展开状态
  void toggleDepartmentExpansion(String departmentId) {
    if (_expandedDepartmentIds.contains(departmentId)) {
      _expandedDepartmentIds.remove(departmentId);
    } else {
      _expandedDepartmentIds.add(departmentId);
    }
    notifyListeners();
  }
  
  /// 检查部门是否展开
  bool isDepartmentExpanded(String departmentId) {
    return _expandedDepartmentIds.contains(departmentId);
  }
  
  /// 检查部门是否被选中
  bool isDepartmentSelected(String departmentId) {
    return _selectedDepartmentIds.contains(departmentId);
  }
  
  /// 检查部门是否被高亮
  bool isDepartmentHighlighted(String departmentId) {
    return _highlightedDepartmentId == departmentId;
  }
  
  /// 获取所有被选中的部门对象
  List<DepartmentModel> getSelectedDepartments() {
    return _allDepartments
        .where((dept) => dept.id != null && _selectedDepartmentIds.contains(dept.id!))
        .toList();
  }

  // 私有方法区域
  
  /// 设置加载状态
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }
  
  /// 设置错误信息
  void _setError(String? error) {
    if (_errorMessage != error) {
      _errorMessage = error;
      notifyListeners();
    }
  }
  
  /// 初始化默认展开状态
  void _initializeDefaultExpansion() {
    // 找到第一个根节点并展开
    final rootDepartment = _allDepartments.firstWhere(
      (dept) => dept.parentId == null || dept.parentId!.isEmpty,
      orElse: () => DepartmentModel(),
    );
    
    if (rootDepartment.id != null) {
      _expandedDepartmentIds.add(rootDepartment.id!);
      _highlightedDepartmentId = rootDepartment.id;
    }
  }
  
  /// 应用搜索过滤
  void _applySearchFilter() {
    if (_searchQuery == null || _searchQuery!.isEmpty) {
      // 没有搜索条件，显示所有部门
      _filteredDepartments = List.from(_allDepartments);
    } else {
      // 有搜索条件，进行过滤并保持层级结构
      _filteredDepartments = _filterDepartmentsWithHierarchy(_searchQuery!);
    }
  }
  
  /// 过滤部门并保持层级结构完整
  /// 
  /// 当部门匹配搜索条件时，其所有上级部门也会被包含在结果中
  List<DepartmentModel> _filterDepartmentsWithHierarchy(String query) {
    final Set<String> includedIds = <String>{};
    final String lowerQuery = query.toLowerCase();
    
    // 第一步：找到所有匹配的部门
    for (final dept in _allDepartments) {
      if (dept.departmentName.toLowerCase().contains(lowerQuery)) {
        includedIds.add(dept.id ?? '');
        
        // 第二步：包含所有上级部门
        _includeParentDepartments(dept, includedIds);
      }
    }
    
    // 第三步：返回过滤后的部门列表
    return _allDepartments
        .where((dept) => includedIds.contains(dept.id ?? ''))
        .toList();
  }
  
  /// 递归包含上级部门
  void _includeParentDepartments(DepartmentModel department, Set<String> includedIds) {
    if (department.parentId != null && department.parentId!.isNotEmpty) {
      includedIds.add(department.parentId!);
      
      // 找到父部门并递归处理
      final parentDept = _allDepartments.firstWhere(
        (dept) => dept.id == department.parentId,
        orElse: () => DepartmentModel(),
      );
      
      if (parentDept.id != null) {
        _includeParentDepartments(parentDept, includedIds);
      }
    }
  }
}
